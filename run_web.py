#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ملف تشغيل الواجهة الويب لوكيل أخبار الألعاب
يشغل خادم Flask للتحكم في الوكيل عبر الواجهة الويب
"""

import os
import sys
import asyncio
from pathlib import Path

# إضافة المسار الحالي لـ Python path
current_dir = Path(__file__).parent
sys.path.insert(0, str(current_dir))

# استيراد الخادم
from web_api import app, load_settings
from modules.logger import logger

def main():
    """تشغيل الواجهة الويب"""
    try:
        print("🌐 بدء تشغيل واجهة ويب وكيل أخبار الألعاب...")
        print("=" * 60)
        print("📋 معلومات التشغيل:")
        print(f"   • الخادم: http://localhost:5000")
        print(f"   • الواجهة: http://localhost:5000")
        print(f"   • API: http://localhost:5000/api")
        print("=" * 60)

        # تحميل الإعدادات
        load_settings()

        # تشغيل الخادم
        logger.info("🌐 بدء تشغيل خادم الواجهة الويب...")
        app.run(
            host='0.0.0.0',
            port=5000,
            debug=False,
            threaded=True
        )

    except KeyboardInterrupt:
        print("\n⏹️ تم إيقاف الخادم بواسطة المستخدم")
        logger.info("⏹️ تم إيقاف خادم الواجهة الويب")
    except Exception as e:
        print(f"❌ خطأ في تشغيل الخادم: {e}")
        logger.error(f"❌ خطأ في تشغيل خادم الواجهة الويب: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()