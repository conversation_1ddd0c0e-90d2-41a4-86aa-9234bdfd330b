# API خلفي للتحكم في وكيل أخبار الألعاب
from flask import Flask, jsonify, request, send_from_directory, redirect, url_for
from flask_cors import CORS
import threading
import asyncio
import json
import os
import time
from datetime import datetime, timedelta
from typing import Dict, List, Optional
import logging

# استيراد مكونات الوكيل
from modules.logger import logger
from modules.database import db
from modules.web_approval_system import web_approval_system
from config.settings import BotConfig

app = Flask(__name__)
CORS(app)  # للسماح بطلبات من الواجهة الأمامية

# متغيرات عامة لحالة الوكيل
agent_status = {
    'status': 'offline',  # offline, online, processing, paused
    'start_time': None,
    'last_activity': None,
    'stats': {
        'articlesPublished': 0,
        'contentProcessed': 0,
        'successRate': 0,
        'uptime': 0
    }
}

agent_thread = None
agent_instance = None
pending_approvals = []
agent_settings = {
    'searchInterval': 2,
    'articlesPerCycle': 5,
    'preferredDialect': 'standard',
    'autoApproval': False
}

class WebAgentController:
    """تحكم في الوكيل من خلال الواجهة الويب"""

    def __init__(self):
        self.is_running = False
        self.is_paused = False
        self.agent = None

    async def start_agent(self):
        """بدء تشغيل الوكيل"""
        try:
            if self.is_running:
                return False, "الوكيل يعمل بالفعل"

            # استيراد الوكيل
            from main import GamingNewsBot

            self.agent = GamingNewsBot()
            self.is_running = True
            self.is_paused = False

            # تشغيل الوكيل في مهمة منفصلة
            await self.agent.run()

            return True, "تم تشغيل الوكيل بنجاح"

        except Exception as e:
            logger.error(f"خطأ في تشغيل الوكيل: {e}")
            return False, f"خطأ في تشغيل الوكيل: {str(e)}"

    def stop_agent(self):
        """إيقاف الوكيل"""
        try:
            if not self.is_running:
                return False, "الوكيل غير مشغل"

            self.is_running = False
            self.is_paused = False

            if self.agent:
                # إيقاف آمن للوكيل
                asyncio.create_task(self.agent.graceful_shutdown())

            return True, "تم إيقاف الوكيل بنجاح"

        except Exception as e:
            logger.error(f"خطأ في إيقاف الوكيل: {e}")
            return False, f"خطأ في إيقاف الوكيل: {str(e)}"

    def pause_agent(self):
        """إيقاف مؤقت للوكيل"""
        try:
            if not self.is_running:
                return False, "الوكيل غير مشغل"

            self.is_paused = True
            return True, "تم إيقاف الوكيل مؤقتاً"

        except Exception as e:
            logger.error(f"خطأ في الإيقاف المؤقت: {e}")
            return False, f"خطأ في الإيقاف المؤقت: {str(e)}"

    def resume_agent(self):
        """استئناف تشغيل الوكيل"""
        try:
            if not self.is_running:
                return False, "الوكيل غير مشغل"

            self.is_paused = False
            return True, "تم استئناف تشغيل الوكيل"

        except Exception as e:
            logger.error(f"خطأ في الاستئناف: {e}")
            return False, f"خطأ في الاستئناف: {str(e)}"

    async def start_cycle(self):
        """بدء دورة جديدة"""
        try:
            if not self.is_running or self.is_paused:
                return False, "الوكيل غير مشغل أو متوقف مؤقتاً"

            if self.agent:
                # بدء دورة جديدة
                result = await self.agent._main_cycle()
                return True, f"تم بدء دورة جديدة - نشر {result.get('published_count', 0)} مقالات"

            return False, "الوكيل غير متاح"

        except Exception as e:
            logger.error(f"خطأ في بدء الدورة: {e}")
            return False, f"خطأ في بدء الدورة: {str(e)}"

    def get_status(self):
        """الحصول على حالة الوكيل"""
        status = 'offline'
        if self.is_running:
            if self.is_paused:
                status = 'paused'
            else:
                status = 'online'

        return {
            'status': status,
            'is_running': self.is_running,
            'is_paused': self.is_paused,
            'start_time': agent_status.get('start_time'),
            'last_activity': agent_status.get('last_activity')
        }

# إنشاء مثيل من المتحكم
web_controller = WebAgentController()

# Routes للواجهة الويب
@app.route('/')
def index():
    """صفحة الواجهة الرئيسية"""
    return send_from_directory('web_interface', 'index.html')

@app.route('/<path:filename>')
def static_files(filename):
    """ملفات الواجهة الثابتة"""
    return send_from_directory('web_interface', filename)

# API Routes
@app.route('/api/status')
def get_status():
    """الحصول على حالة الوكيل"""
    try:
        # تحديث الإحصائيات من قاعدة البيانات
        stats = db.get_stats_summary(1)  # إحصائيات اليوم

        # حساب وقت التشغيل
        uptime = 0
        if agent_status['start_time']:
            uptime = int((datetime.now() - agent_status['start_time']).total_seconds())

        status_data = {
            'status': agent_status['status'],
            'stats': {
                'articlesPublished': stats.get('articles_published', 0) if stats else 0,
                'contentProcessed': stats.get('articles_processed', 0) if stats else 0,
                'successRate': calculate_success_rate(stats) if stats else 0,
                'uptime': uptime
            },
            'activity': agent_status.get('last_activity', 'لا يوجد نشاط')
        }

        return jsonify(status_data)

    except Exception as e:
        logger.error(f"خطأ في الحصول على الحالة: {e}")
        return jsonify({'error': str(e)}), 500

@app.route('/api/agent/start', methods=['POST'])
def start_agent():
    """تشغيل الوكيل"""
    try:
        def run_agent():
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)

            try:
                success, message = loop.run_until_complete(web_controller.start_agent())
                if success:
                    agent_status['status'] = 'online'
                    agent_status['start_time'] = datetime.now()
                    agent_status['last_activity'] = 'تم تشغيل الوكيل'
                    logger.info("✅ تم تشغيل الوكيل من الواجهة الويب")
                else:
                    logger.error(f"❌ فشل في تشغيل الوكيل: {message}")
            except Exception as e:
                logger.error(f"❌ خطأ في تشغيل الوكيل: {e}")
            finally:
                loop.close()

        # تشغيل الوكيل في thread منفصل
        global agent_thread
        if agent_thread and agent_thread.is_alive():
            return jsonify({'error': 'الوكيل يعمل بالفعل'}), 400

        agent_thread = threading.Thread(target=run_agent, daemon=True)
        agent_thread.start()

        return jsonify({'message': 'تم بدء تشغيل الوكيل'})

    except Exception as e:
        logger.error(f"خطأ في تشغيل الوكيل: {e}")
        return jsonify({'error': str(e)}), 500

@app.route('/api/agent/stop', methods=['POST'])
def stop_agent():
    """إيقاف الوكيل"""
    try:
        success, message = web_controller.stop_agent()

        if success:
            agent_status['status'] = 'offline'
            agent_status['start_time'] = None
            agent_status['last_activity'] = 'تم إيقاف الوكيل'
            logger.info("✅ تم إيقاف الوكيل من الواجهة الويب")
            return jsonify({'message': message})
        else:
            return jsonify({'error': message}), 400

    except Exception as e:
        logger.error(f"خطأ في إيقاف الوكيل: {e}")
        return jsonify({'error': str(e)}), 500

@app.route('/api/agent/pause', methods=['POST'])
def pause_agent():
    """إيقاف مؤقت للوكيل"""
    try:
        success, message = web_controller.pause_agent()

        if success:
            agent_status['status'] = 'paused'
            agent_status['last_activity'] = 'تم إيقاف الوكيل مؤقتاً'
            return jsonify({'message': message})
        else:
            return jsonify({'error': message}), 400

    except Exception as e:
        logger.error(f"خطأ في الإيقاف المؤقت: {e}")
        return jsonify({'error': str(e)}), 500

@app.route('/api/agent/start-cycle', methods=['POST'])
def start_cycle():
    """بدء دورة جديدة"""
    try:
        def run_cycle():
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)

            try:
                success, message = loop.run_until_complete(web_controller.start_cycle())
                if success:
                    agent_status['status'] = 'processing'
                    agent_status['last_activity'] = 'بدء دورة جديدة'
                    logger.info("✅ تم بدء دورة جديدة من الواجهة الويب")
                else:
                    logger.error(f"❌ فشل في بدء الدورة: {message}")
            except Exception as e:
                logger.error(f"❌ خطأ في بدء الدورة: {e}")
            finally:
                loop.close()

        # تشغيل الدورة في thread منفصل
        cycle_thread = threading.Thread(target=run_cycle, daemon=True)
        cycle_thread.start()

        return jsonify({'message': 'تم بدء دورة جديدة'})

    except Exception as e:
        logger.error(f"خطأ في بدء الدورة: {e}")
        return jsonify({'error': str(e)}), 500

@app.route('/api/content')
def get_content():
    """الحصول على المحتوى المنشور"""
    try:
        # الحصول على آخر 20 مقال من قاعدة البيانات
        articles = db.get_recent_articles(20)

        content_list = []
        for article in articles:
            content_list.append({
                'id': article.get('id'),
                'title': article.get('title'),
                'published_date': article.get('published_date'),
                'source': article.get('source_type', 'غير محدد'),
                'blogger_url': article.get('blogger_url'),
                'category': article.get('category', 'أخبار الألعاب')
            })

        return jsonify(content_list)

    except Exception as e:
        logger.error(f"خطأ في الحصول على المحتوى: {e}")
        return jsonify({'error': str(e)}), 500

@app.route('/api/logs')
def get_logs():
    """الحصول على سجلات النظام"""
    try:
        log_file = BotConfig.LOG_FILE
        if os.path.exists(log_file):
            with open(log_file, 'r', encoding='utf-8') as f:
                # قراءة آخر 100 سطر
                lines = f.readlines()
                recent_logs = ''.join(lines[-100:])
                return recent_logs
        else:
            return "لا توجد سجلات متاحة"

    except Exception as e:
        logger.error(f"خطأ في قراءة السجلات: {e}")
        return f"خطأ في قراءة السجلات: {str(e)}", 500

@app.route('/api/settings', methods=['GET', 'POST'])
def handle_settings():
    """إدارة إعدادات الوكيل"""
    global agent_settings

    if request.method == 'GET':
        return jsonify(agent_settings)

    elif request.method == 'POST':
        try:
            new_settings = request.get_json()

            # تحديث الإعدادات
            agent_settings.update(new_settings)

            # حفظ الإعدادات في ملف
            settings_file = 'web_settings.json'
            with open(settings_file, 'w', encoding='utf-8') as f:
                json.dump(agent_settings, f, ensure_ascii=False, indent=2)

            logger.info("✅ تم تحديث إعدادات الوكيل من الواجهة الويب")
            return jsonify({'message': 'تم حفظ الإعدادات بنجاح'})

        except Exception as e:
            logger.error(f"خطأ في حفظ الإعدادات: {e}")
            return jsonify({'error': str(e)}), 500

@app.route('/api/approvals/pending')
def get_pending_approvals():
    """الحصول على الموافقات المعلقة"""
    try:
        approvals = web_approval_system.get_pending_approvals()
        return jsonify(approvals)
    except Exception as e:
        logger.error(f"خطأ في الحصول على الموافقات: {e}")
        return jsonify({'error': str(e)}), 500

@app.route('/api/approvals/<approval_id>/approve', methods=['POST'])
def approve_content(approval_id):
    """الموافقة على المحتوى"""
    try:
        def run_approval():
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)

            try:
                success = loop.run_until_complete(
                    web_approval_system.approve_content(approval_id, "web_user")
                )
                return success
            finally:
                loop.close()

        # تشغيل الموافقة في thread منفصل
        approval_thread = threading.Thread(target=run_approval, daemon=True)
        approval_thread.start()
        approval_thread.join(timeout=5)  # انتظار 5 ثوان كحد أقصى

        return jsonify({'message': 'تمت الموافقة على المحتوى'})

    except Exception as e:
        logger.error(f"خطأ في الموافقة: {e}")
        return jsonify({'error': str(e)}), 500

@app.route('/api/approvals/<approval_id>/reject', methods=['POST'])
def reject_content(approval_id):
    """رفض المحتوى"""
    try:
        data = request.get_json() or {}
        reason = data.get('reason', 'تم الرفض من الواجهة الويب')

        def run_rejection():
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)

            try:
                success = loop.run_until_complete(
                    web_approval_system.reject_content(approval_id, "web_user", reason)
                )
                return success
            finally:
                loop.close()

        # تشغيل الرفض في thread منفصل
        rejection_thread = threading.Thread(target=run_rejection, daemon=True)
        rejection_thread.start()
        rejection_thread.join(timeout=5)  # انتظار 5 ثوان كحد أقصى

        return jsonify({'message': 'تم رفض المحتوى'})

    except Exception as e:
        logger.error(f"خطأ في الرفض: {e}")
        return jsonify({'error': str(e)}), 500

@app.route('/api/approvals/stats')
def get_approval_stats():
    """الحصول على إحصائيات الموافقات"""
    try:
        stats = web_approval_system.get_approval_stats()
        return jsonify(stats)
    except Exception as e:
        logger.error(f"خطأ في الحصول على إحصائيات الموافقات: {e}")
        return jsonify({'error': str(e)}), 500

@app.route('/api/approvals/auto-approve', methods=['POST'])
def set_auto_approval():
    """تفعيل/تعطيل الموافقة التلقائية"""
    try:
        data = request.get_json()
        enabled = data.get('enabled', False)
        timeout = data.get('timeout', 300)

        web_approval_system.set_auto_approval(enabled, timeout)

        # تحديث الإعدادات العامة
        global agent_settings
        agent_settings['autoApproval'] = enabled

        status = "مفعلة" if enabled else "معطلة"
        return jsonify({'message': f'تم تحديث الموافقة التلقائية - {status}'})

    except Exception as e:
        logger.error(f"خطأ في تحديث الموافقة التلقائية: {e}")
        return jsonify({'error': str(e)}), 500

@app.route('/api/analytics')
def get_analytics():
    """الحصول على التحليلات"""
    try:
        # الحصول على إحصائيات من قاعدة البيانات
        stats = db.get_stats_summary(7)  # آخر 7 أيام

        analytics_data = {
            'totalArticles': stats.get('total_articles', 0) if stats else 0,
            'dailyRate': stats.get('daily_average', 0) if stats else 0,
            'topSource': get_top_source(),
            'chartData': {
                'labels': get_chart_labels(),
                'values': get_chart_values()
            }
        }

        return jsonify(analytics_data)

    except Exception as e:
        logger.error(f"خطأ في الحصول على التحليلات: {e}")
        return jsonify({'error': str(e)}), 500

# دوال مساعدة
def calculate_success_rate(stats):
    """حساب معدل النجاح"""
    if not stats:
        return 0

    processed = stats.get('articles_processed', 0)
    published = stats.get('articles_published', 0)

    if processed == 0:
        return 0

    return round((published / processed) * 100, 1)

def get_top_source():
    """الحصول على أفضل مصدر"""
    try:
        # يمكن تحسين هذا بالحصول على البيانات من قاعدة البيانات
        return "YouTube"
    except:
        return "غير محدد"

def get_chart_labels():
    """الحصول على تسميات الرسم البياني"""
    labels = []
    for i in range(7):
        date = datetime.now() - timedelta(days=i)
        labels.append(date.strftime('%Y-%m-%d'))
    return labels[::-1]  # عكس الترتيب

def get_chart_values():
    """الحصول على قيم الرسم البياني"""
    # يمكن تحسين هذا بالحصول على البيانات الفعلية من قاعدة البيانات
    return [2, 3, 1, 4, 2, 3, 5]

def load_settings():
    """تحميل الإعدادات من الملف"""
    global agent_settings
    settings_file = 'web_settings.json'

    if os.path.exists(settings_file):
        try:
            with open(settings_file, 'r', encoding='utf-8') as f:
                agent_settings.update(json.load(f))
            logger.info("✅ تم تحميل إعدادات الواجهة الويب")
        except Exception as e:
            logger.error(f"خطأ في تحميل الإعدادات: {e}")

# تشغيل الخادم
if __name__ == '__main__':
    # تحميل الإعدادات
    load_settings()

    # تشغيل الخادم
    logger.info("🌐 بدء تشغيل خادم الواجهة الويب...")
    app.run(host='0.0.0.0', port=5000, debug=False)