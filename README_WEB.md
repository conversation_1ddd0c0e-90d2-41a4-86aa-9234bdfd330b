# وكيل أخبار الألعاب - الواجهة الويب

## نظرة عامة

تم تحديث وكيل أخبار الألعاب ليعمل بواجهة ويب احترافية بدلاً من Telegram. الآن يمكنك التحكم في الوكيل بالكامل من خلال متصفح الويب.

## ✨ المميزات الجديدة

### 🌐 واجهة ويب احترافية
- لوحة تحكم شاملة باللغة العربية
- إحصائيات في الوقت الفعلي
- تحكم كامل في الوكيل (تشغيل/إيقاف/إيقاف مؤقت)
- عرض المحتوى المنشور
- نظام موافقة متقدم

### 📋 نظام الموافقة الجديد
- موافقة على المحتوى عبر الواجهة الويب
- إمكانية الموافقة التلقائية
- تتبع حالة الموافقات
- سجل كامل للموافقات والرفض

### 📊 التحليلات والإحصائيات
- إحصائيات يومية وأسبوعية
- رسوم بيانية تفاعلية
- تتبع الأداء
- تحليل المصادر

## 🚀 التشغيل

### 1. التشغيل السريع (مع فتح تلقائي للمتصفح)

#### لنظام Windows:
```bash
# انقر مرتين على الملف أو شغل من Command Prompt
start_web.bat
```

#### لنظام Linux/Mac:
```bash
# اجعل الملف قابل للتنفيذ أولاً (مرة واحدة فقط)
chmod +x start_web.sh
# ثم شغل الملف
./start_web.sh
```

#### الطريقة العامة:
```bash
python run_web.py
```

### 2. الوصول للواجهة

**سيتم فتح المتصفح تلقائياً** خلال ثوانٍ من التشغيل!

إذا لم يفتح تلقائياً، افتح المتصفح يدوياً وانتقل إلى:
```
http://localhost:5000
```

### 3. استخدام الواجهة

#### لوحة التحكم الرئيسية
- **تشغيل الوكيل**: ابدأ تشغيل الوكيل
- **إيقاف مؤقت**: أوقف الوكيل مؤقتاً
- **إيقاف كامل**: أوقف الوكيل نهائياً
- **بدء دورة جديدة**: ابدأ دورة بحث جديدة

#### تبويب المحتوى المنشور
- عرض آخر المقالات المنشورة
- روابط مباشرة للمقالات على Blogger
- معلومات تفصيلية عن كل مقال

#### تبويب الموافقة على المحتوى
- عرض المحتوى في انتظار الموافقة
- أزرار الموافقة والرفض
- معاينة المحتوى قبل الموافقة

#### تبويب الإعدادات
- تحديد فترة البحث
- عدد المقالات لكل دورة
- اللهجة المفضلة
- تفعيل/تعطيل الموافقة التلقائية

#### تبويب التحليلات
- رسوم بيانية للأداء
- إحصائيات مفصلة
- تحليل المصادر

## 🔧 الإعدادات

### إعدادات الواجهة الويب

يمكن تخصيص الإعدادات من خلال:
1. تبويب الإعدادات في الواجهة
2. ملف `web_settings.json` (يتم إنشاؤه تلقائياً)

### إعدادات الموافقة التلقائية

```json
{
  "autoApproval": true,
  "autoApprovalTimeout": 300
}
```

## 🧪 الاختبار

### تشغيل اختبارات النظام

```bash
python test_web_system.py
```

هذا سيختبر:
- قاعدة البيانات
- نظام الموافقة الويب
- الناشر (Blogger فقط)
- ملفات الواجهة الويب
- نقاط API

## 📁 هيكل الملفات الجديدة

```
├── web_interface/          # ملفات الواجهة الويب
│   ├── index.html         # الصفحة الرئيسية
│   ├── styles.css         # ملف التصميم
│   └── script.js          # ملف JavaScript
├── modules/
│   └── web_approval_system.py  # نظام الموافقة الويب
├── web_api.py             # خادم API
├── run_web.py             # ملف تشغيل الواجهة (مع فتح تلقائي)
├── start_web.bat          # تشغيل سريع لـ Windows
├── start_web.sh           # تشغيل سريع لـ Linux/Mac
├── test_web_system.py     # اختبارات النظام
└── README_WEB.md          # هذا الملف
```

## 🔄 التغييرات المهمة

### ✅ تم إضافة
- واجهة ويب احترافية كاملة
- نظام موافقة ويب متقدم
- API خلفي شامل
- اختبارات النظام
- تحليلات وإحصائيات
- **فتح تلقائي للمتصفح** عند التشغيل
- ملفات تشغيل سريع لجميع الأنظمة

### ❌ تم إزالة
- جميع اعتمادات Telegram
- نظام الموافقة عبر Telegram
- النشر على Telegram
- إعدادات Telegram

### 🔄 تم تحديث
- الناشر ليعمل مع Blogger فقط
- نظام الموافقة ليعمل عبر الويب
- الملف الرئيسي لإزالة Telegram
- ملفات الإعدادات

## 🌟 المميزات المتقدمة

### 🚀 التشغيل السهل
- **فتح تلقائي للمتصفح** عند التشغيل
- ملفات تشغيل سريع لجميع الأنظمة
- رسائل ترحيب وإرشادات واضحة

### 🔄 التحديث التلقائي
- تحديث الحالة كل 5 ثوان
- إشعارات في الوقت الفعلي
- تحديث الإحصائيات تلقائياً

### 📱 التصميم المتجاوب
- يعمل على جميع الأجهزة
- تصميم متجاوب للهواتف والأجهزة اللوحية
- واجهة سهلة الاستخدام

### 🔒 الأمان
- API آمن
- تشفير الاتصالات
- حماية من الوصول غير المصرح

## 🆘 استكشاف الأخطاء

### مشكلة: الواجهة لا تفتح
**الحل**: تأكد من تشغيل `python run_web.py` أولاً

### مشكلة: الوكيل لا يبدأ
**الحل**: تحقق من:
1. إعدادات Blogger
2. ملفات الاعتماد
3. سجلات الأخطاء

### مشكلة: الموافقة لا تعمل
**الحل**: تحقق من:
1. تشغيل خادم الواجهة
2. إعدادات الموافقة التلقائية
3. سجلات النظام

## 📞 الدعم

إذا واجهت أي مشاكل:
1. شغل `python test_web_system.py` للتشخيص
2. راجع ملفات السجلات
3. تحقق من الإعدادات

## 🎯 الخطوات التالية

### للبدء السريع:
1. **انقر مرتين على** `start_web.bat` (Windows) أو `./start_web.sh` (Linux/Mac)
2. **انتظر** - سيفتح المتصفح تلقائياً!
3. **ابدأ تشغيل الوكيل** من الواجهة
4. **راقب الأداء** والإحصائيات
5. **وافق على المحتوى** حسب الحاجة

### للتشغيل اليدوي:
1. شغل الواجهة الويب: `python run_web.py`
2. سيفتح المتصفح تلقائياً على `http://localhost:5000`
3. إذا لم يفتح، افتح المتصفح يدوياً

---

**ملاحظة**: تم إزالة جميع اعتمادات Telegram نهائياً. النظام الآن يعتمد على الواجهة الويب فقط للتحكم والموافقة.